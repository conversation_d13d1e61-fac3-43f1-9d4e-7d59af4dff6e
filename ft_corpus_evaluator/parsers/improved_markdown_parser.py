import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field

try:
    import mistune
    from mistune import HTMLRenderer
except ImportError:
    raise ImportError("mistune is required. Please install it with: pip install mistune>=3.0.0")

from ..models.corpus_model import (
    FTCorpus, RdcInfo, TestInfo, TagIdentification, CodeSnippet,
    DependencyCode, SimilarTestCase, SimilarCodeInfo, GenerationRequirements,
    ImplementationSteps, OutputFormat, OutputRequirements, TargetTestCaseDescription
)


class ImprovedMarkdownCorpusParser:
    """改进的基于mistune的markdown语料解析器"""
    
    def __init__(self):
        self.markdown = mistune.create_markdown()
    
    def parse_file(self, file_path: Path) -> FTCorpus:
        """解析markdown文件"""
        content = file_path.read_text(encoding='utf-8')
        
        # 使用简单的文本分割方法，结合mistune处理代码块
        sections = self._split_content_by_headers(content)
        
        # 提取各个部分的信息
        rdc_info = self._parse_rdc_info(sections)
        test_info = self._parse_test_info(sections)
        tag_identification = self._parse_tag_identification(sections)
        code_snippets = self._parse_code_snippets(content)
        
        # 解析新字段
        dependency_code = self._parse_dependency_code(sections)
        similar_code_info = self._parse_similar_code_info(sections)
        generation_requirements = self._parse_generation_requirements(sections)
        implementation_steps = self._parse_implementation_steps(sections)
        output_format = self._parse_output_format(sections)
        output_requirements = self._parse_output_requirements(sections)
        target_test_case_description = self._parse_target_test_case_description(sections)
        
        return FTCorpus(
            file_path=str(file_path),
            rdc_info=rdc_info,
            test_info=test_info,
            tag_identification=tag_identification,
            code_snippets=code_snippets,
            raw_content=content,
            dependency_code=dependency_code,
            similar_code_info=similar_code_info,
            generation_requirements=generation_requirements,
            implementation_steps=implementation_steps,
            output_format=output_format,
            output_requirements=output_requirements,
            target_test_case_description=target_test_case_description
        )
    
    def _split_content_by_headers(self, content: str) -> Dict[str, str]:
        """按标题分割内容"""
        sections = {}

        # 分割RdcInfo部分
        rdc_match = re.search(r'\*\*RdcInfo\*\*(.*?)(?=\n##|\n#|$)', content, re.DOTALL)
        if rdc_match:
            sections['RdcInfo'] = rdc_match.group(1)

        # 首先分割一级标题（#），一级标题包含其下的所有二级标题内容
        level1_pattern = r'\n#\s+([^\n]+)\n(.*?)(?=\n#\s+|\n\*\*QUESTION\*\*|\n## ANSAWER|$)'
        level1_matches = re.findall(level1_pattern, content, re.DOTALL)

        for title, section_content in level1_matches:
            sections[title.strip()] = section_content.strip()

        # 然后分割独立的二级标题（##），只有那些不在一级标题下的
        level2_pattern = r'\n##\s+([^\n]+)\n(.*?)(?=\n#{1,2}\s+|\n\*\*QUESTION\*\*|\n## ANSAWER|$)'
        level2_matches = re.findall(level2_pattern, content, re.DOTALL)

        for title, section_content in level2_matches:
            title = title.strip()
            # 检查这个二级标题是否已经被包含在某个一级标题中
            is_under_level1 = False
            for level1_title, level1_content in sections.items():
                if f"## {title}" in level1_content:
                    is_under_level1 = True
                    break

            # 只有独立的二级标题才添加到sections中
            if not is_under_level1:
                sections[title] = section_content.strip()

        return sections
    
    def _parse_rdc_info(self, sections: Dict[str, str]) -> RdcInfo:
        """解析RdcInfo信息"""
        rdc_content = sections.get('RdcInfo', '')
        
        # 提取字段
        rdc_id = self._extract_field(rdc_content, r'rdc_id:(.+)')
        repo_name = self._extract_field(rdc_content, r'repo_name:(.+)')
        gerrit_link = self._extract_field(rdc_content, r'gerrit_link:(.+)')
        date = self._extract_field(rdc_content, r'date:(.+)')
        final_test = self._extract_field(rdc_content, r'final_test:(.+)')
        compile_script = self._extract_field(rdc_content, r'compile_script:(.+)')
        compile_command_json_path = self._extract_field(rdc_content, r'compile_command_json_path:(.+)')
        
        return RdcInfo(
            rdc_id=rdc_id,
            repo_name=repo_name,
            gerrit_link=gerrit_link,
            date=date,
            final_test=final_test,
            compile_script=compile_script,
            compile_command_json_path=compile_command_json_path
        )
    
    def _parse_test_info(self, sections: Dict[str, str]) -> TestInfo:
        """解析TestInfo信息"""
        test_content = sections.get('TestInfo', '')
        if not test_content:
            return TestInfo("")

        # 使用原解析器的逻辑，但调整正则表达式
        test_title = self._extract_field_multiline(test_content, r'### 测试标题[：:]?\s*\n(.+)')
        preconditions = self._extract_field_multiline(test_content, r'### 预[制置]条件[：:]?\s*\n(.+)')
        tc_steps = self._extract_field_multiline(test_content, r'### TC步骤[：:]?\s*\n(.*?)(?=### |$)')
        tc_expected_results = self._extract_field_multiline(test_content, r'### TC预期结果[：:]?\s*\n(.*?)(?=### |$)')
        expected_results = self._extract_field_multiline(test_content, r'### 预期结果[：:]?\s*\n(.*?)(?=### |$)')
        pass_criteria = self._extract_field_multiline(test_content, r'### (?:验收|通过)准则[：:]?\s*\n(.*?)(?=### |$)')

        return TestInfo(
            test_title=test_title,
            preconditions=preconditions,
            tc_steps=tc_steps,
            tc_expected_results=tc_expected_results,
            expected_results=expected_results,
            pass_criteria=pass_criteria
        )
    
    def _parse_tag_identification(self, sections: Dict[str, str]) -> TagIdentification:
        """解析Tag Identification信息"""
        tag_content = sections.get('Tag Identification', '')
        if not tag_content:
            return TagIdentification()
        
        business_tags = self._extract_list_field(tag_content, r'### business_content_scence_tag(.*?)(?=###|$)')
        code_tags = self._extract_list_field(tag_content, r'### code_modify_scence_tag(.*?)(?=###|$)')
        
        return TagIdentification(
            business_content_scene_tags=business_tags,
            code_modify_scene_tags=code_tags
        )
    
    def _parse_code_snippets(self, content: str) -> List[CodeSnippet]:
        """解析代码片段"""
        snippets = []
        
        # 查找所有代码块
        code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', content, re.DOTALL)
        
        for language, code in code_blocks:
            # 尝试从周围文本中提取文件路径
            file_path = self._find_file_path_near_code(content, code[:100])
            
            snippets.append(CodeSnippet(
                file_path=file_path,
                language=language or "unknown",
                content=code.strip()
            ))
        
        return snippets
    
    def _parse_dependency_code(self, sections: Dict[str, str]) -> List[DependencyCode]:
        """解析目标用例生成可能依赖代码"""
        dependency_codes = []
        
        dep_content = sections.get('目标用例生成可能依赖代码', '')
        if not dep_content:
            return dependency_codes
        
        # 提取代码路径
        file_path = self._extract_field(dep_content, r'代码路径[：:]?\s*(.+)')
        
        # 提取代码块
        code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', dep_content, re.DOTALL)
        for language, code in code_blocks:
            dependency_codes.append(DependencyCode(
                file_path=file_path,
                language=language or "unknown",
                content=code.strip(),
                description="目标用例生成可能依赖代码"
            ))
        
        return dependency_codes
    
    def _parse_similar_code_info(self, sections: Dict[str, str]) -> Optional[SimilarCodeInfo]:
        """解析相似代码信息"""
        similar_content = sections.get('相似代码信息', '')
        if not similar_content:
            return None
        
        # 解析相似测试用例
        similar_test_case = None
        test_case_match = re.search(r'## 相似文本测试用例描述\n(.*?)(?=## |$)', similar_content, re.DOTALL)
        if test_case_match and test_case_match.group(1).strip() != '无':
            # 这里可以进一步解析测试用例详情
            similar_test_case = SimilarTestCase(test_title="相似测试用例")
        
        # 解析相似FT代码
        similar_ft_code = []
        ft_code_match = re.search(r'## 相似用例FT代码\n(.*?)(?=## |# |$)', similar_content, re.DOTALL)
        if ft_code_match:
            ft_section = ft_code_match.group(1)
            file_path = self._extract_field(ft_section, r'代码路径[：:]?\s*(.+)')
            code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', ft_section, re.DOTALL)
            for language, code in code_blocks:
                similar_ft_code.append(CodeSnippet(
                    file_path=file_path,
                    language=language or "unknown",
                    content=code.strip(),
                    description="相似用例FT代码"
                ))
        
        # 解析相似用例代码依赖
        similar_dependency_code = []
        dep_code_match = re.search(r'## 相似用例代码依赖\n(.*?)(?=## |$)', similar_content, re.DOTALL)
        if dep_code_match and dep_code_match.group(1).strip() != '无':
            dep_section = dep_code_match.group(1)
            file_path = self._extract_field(dep_section, r'代码路径[：:]?\s*(.+)')
            code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', dep_section, re.DOTALL)
            for language, code in code_blocks:
                similar_dependency_code.append(CodeSnippet(
                    file_path=file_path,
                    language=language or "unknown",
                    content=code.strip(),
                    description="相似用例代码依赖"
                ))
        
        return SimilarCodeInfo(
            similar_test_case=similar_test_case,
            similar_ft_code=similar_ft_code,
            similar_dependency_code=similar_dependency_code
        )
    
    def _parse_generation_requirements(self, sections: Dict[str, str]) -> Optional[GenerationRequirements]:
        """解析代码生成要求"""
        content = sections.get('代码生成要求', '')
        return GenerationRequirements(content=content) if content else None
    
    def _parse_implementation_steps(self, sections: Dict[str, str]) -> Optional[ImplementationSteps]:
        """解析建议的实现步骤"""
        content = sections.get('建议的实现步骤', '')
        return ImplementationSteps(content=content) if content else None
    
    def _parse_output_format(self, sections: Dict[str, str]) -> Optional[OutputFormat]:
        """解析输出格式"""
        content = sections.get('输出格式', '')
        return OutputFormat(content=content) if content else None
    
    def _parse_output_requirements(self, sections: Dict[str, str]) -> Optional[OutputRequirements]:
        """解析输出要求"""
        content = sections.get('输出要求', '')
        return OutputRequirements(content=content) if content else None
    
    def _parse_target_test_case_description(self, sections: Dict[str, str]) -> Optional[TargetTestCaseDescription]:
        """解析目标测试用例描述"""
        content = sections.get('目标测试用例描述', '')
        return TargetTestCaseDescription(content=content) if content else None
    
    def _extract_field(self, content: str, pattern: str) -> str:
        """从内容中提取字段"""
        match = re.search(pattern, content, re.MULTILINE)
        return match.group(1).strip() if match else ""
    
    def _extract_field_multiline(self, content: str, pattern: str) -> str:
        """从内容中提取多行字段"""
        match = re.search(pattern, content, re.MULTILINE | re.DOTALL)
        return match.group(1).strip() if match else ""
    
    def _extract_list_field(self, content: str, pattern: str) -> List[str]:
        """从内容中提取列表字段"""
        match = re.search(pattern, content, re.DOTALL)
        if not match:
            return []
        
        text = match.group(1).strip()
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        return [re.sub(r'^[-*•]\s*', '', re.sub(r'^\d+\.\s*', '', line)) for line in lines if line]
    
    def _find_file_path_near_code(self, content: str, code_snippet: str) -> str:
        """在代码块附近查找文件路径"""
        # 查找代码块在文档中的位置
        code_pos = content.find(code_snippet)
        if code_pos == -1:
            return ""
        
        # 在代码块前面的文本中查找路径
        before_text = content[max(0, code_pos - 500):code_pos]
        path_match = re.search(r'代码路径[：:]?\s*(.+)', before_text)
        return path_match.group(1).strip() if path_match else ""
