import re
from pathlib import Path
from typing import List, Dict, Any, Optional
from ..models.corpus_model import (
    FTCorpus, RdcInfo, TestInfo, TagIdentification, CodeSnippet,
    DependencyCode, SimilarTestCase, SimilarCodeInfo, GenerationRequirements,
    ImplementationSteps, OutputFormat, OutputRequirements, TargetTestCaseDescription
)

class CorpusParser:
    def __init__(self):
        self.section_patterns = {
            'rdc_info': r'\*\*RdcInfo\*\*(.*?)(?=\n##|$)',
            'test_info': r'## TestInfo(.*?)(?=## Tag Identification|$)',
            'tag_identification': r'## Tag Identification(.*?)(?=\n\*\*QUESTION\*\*|$)',
            'dependency_code': r'# 目标用例生成可能依赖代码(.*?)(?=# 相似代码信息|$)',
            'similar_code_info': r'# 相似代码信息(.*?)(?=# 代码生成要求|$)',
            'generation_requirements': r'# 代码生成要求(.*?)(?=# 建议的实现步骤|$)',
            'implementation_steps': r'# 建议的实现步骤(.*?)(?=# 各类用例内容代码修改方法示例|# 输出格式|$)',
            'output_format': r'# 输出格式(.*?)(?=# 输出要求|$)',
            'output_requirements': r'# 输出要求(.*?)(?=# 目标测试用例描述|$)',
            'target_test_case_description': r'# 目标测试用例描述(.*?)(?=## ANSAWER|$)',
            'code_blocks': r'```(\w+)?\n(.*?)\n```'
        }
    
    def parse_file(self, file_path: Path) -> FTCorpus:
        content = file_path.read_text(encoding='utf-8')

        rdc_info = self._parse_rdc_info(content)
        test_info = self._parse_test_info(content)
        tag_identification = self._parse_tag_identification(content)
        code_snippets = self._parse_code_snippets(content)

        # 解析新字段
        dependency_code = self._parse_dependency_code(content)
        similar_code_info = self._parse_similar_code_info(content)
        generation_requirements = self._parse_generation_requirements(content)
        implementation_steps = self._parse_implementation_steps(content)
        output_format = self._parse_output_format(content)
        output_requirements = self._parse_output_requirements(content)
        target_test_case_description = self._parse_target_test_case_description(content)

        return FTCorpus(
            file_path=str(file_path),
            rdc_info=rdc_info,
            test_info=test_info,
            tag_identification=tag_identification,
            code_snippets=code_snippets,
            raw_content=content,
            dependency_code=dependency_code,
            similar_code_info=similar_code_info,
            generation_requirements=generation_requirements,
            implementation_steps=implementation_steps,
            output_format=output_format,
            output_requirements=output_requirements,
            target_test_case_description=target_test_case_description
        )
    
    def _parse_rdc_info(self, content: str) -> RdcInfo:
        rdc_match = re.search(self.section_patterns['rdc_info'], content, re.DOTALL)
        if not rdc_match:
            return RdcInfo("", "", "", "")
        
        rdc_content = rdc_match.group(1)
        
        rdc_id = self._extract_field(rdc_content, r'- rdc_id:(.+)')
        repo_name = self._extract_field(rdc_content, r'- repo_name:(.+)')
        gerrit_link = self._extract_field(rdc_content, r'- gerrit_link:(.+)')
        date = self._extract_field(rdc_content, r'- date:(.+)')
        final_test = self._extract_field(rdc_content, r'- final_test:(.+)')
        compile_script = self._extract_field(rdc_content, r'- compile_script:(.+)')
        compile_command_json_path = self._extract_field(rdc_content, r'- compile_command_json_path:(.+)')

        return RdcInfo(
            rdc_id=rdc_id,
            repo_name=repo_name,
            gerrit_link=gerrit_link,
            date=date,
            final_test=final_test,
            compile_script=compile_script,
            compile_command_json_path=compile_command_json_path
        )
    
    def _parse_test_info(self, content: str) -> TestInfo:
        test_match = re.search(self.section_patterns['test_info'], content, re.DOTALL)
        if not test_match:
            return TestInfo("")
        
        test_content = test_match.group(1)
        
        test_title = self._extract_field_multiline(test_content, r'### 测试标题[：:]?\s*\n(.+)')
        preconditions = self._extract_field_multiline(test_content, r'### 预[制置]条件[：:]?\s*\n(.+)')
        tc_steps = self._extract_field_multiline(test_content, r'### TC步骤[：:]?\s*\n(.*?)(?=### |$)')
        tc_expected_results = self._extract_field_multiline(test_content, r'### TC预期结果[：:]?\s*\n(.*?)(?=### |$)')
        expected_results = self._extract_field_multiline(test_content, r'### 预期结果[：:]?\s*\n(.*?)(?=### |$)')
        pass_criteria = self._extract_field_multiline(test_content, r'### (?:验收|通过)准则[：:]?\s*\n(.*?)(?=### |$)')
        
        return TestInfo(
            test_title=test_title,
            preconditions=preconditions,
            tc_steps=tc_steps,
            tc_expected_results=tc_expected_results,
            expected_results=expected_results,
            pass_criteria=pass_criteria
        )
    
    def _parse_tag_identification(self, content: str) -> TagIdentification:
        tag_match = re.search(self.section_patterns['tag_identification'], content, re.DOTALL)
        if not tag_match:
            return TagIdentification()
        
        tag_content = tag_match.group(1)
        
        business_tags = self._extract_list_field(tag_content, r'### business_content_scence_tag(.*?)(?=###|$)')
        code_tags = self._extract_list_field(tag_content, r'### code_modify_scence_tag(.*?)(?=###|$)')
        
        return TagIdentification(
            business_content_scene_tags=business_tags,
            code_modify_scene_tags=code_tags
        )
    
    def _parse_code_snippets(self, content: str) -> List[CodeSnippet]:
        code_blocks = re.findall(self.section_patterns['code_blocks'], content, re.DOTALL)
        snippets = []
        
        for language, code in code_blocks:
            file_path_match = re.search(r'代码路径[：:]?\s*(.+)', content)
            file_path = file_path_match.group(1) if file_path_match else ""
            
            snippets.append(CodeSnippet(
                file_path=file_path,
                language=language or "unknown",
                content=code.strip()
            ))
        
        return snippets
    
    def _extract_field(self, content: str, pattern: str) -> str:
        match = re.search(pattern, content, re.MULTILINE)
        return match.group(1).strip() if match else ""

    def _extract_field_multiline(self, content: str, pattern: str) -> str:
        match = re.search(pattern, content, re.MULTILINE | re.DOTALL)
        return match.group(1).strip() if match else ""

    def _parse_dependency_code(self, content: str) -> List[DependencyCode]:
        dep_match = re.search(self.section_patterns['dependency_code'], content, re.DOTALL)
        if not dep_match:
            return []

        dep_content = dep_match.group(1)
        dependency_codes = []

        # 提取代码路径
        file_path_match = re.search(r'代码路径[：:]?\s*(.+)', dep_content)
        file_path = file_path_match.group(1).strip() if file_path_match else ""

        # 提取代码块
        code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', dep_content, re.DOTALL)
        for language, code in code_blocks:
            dependency_codes.append(DependencyCode(
                file_path=file_path,
                language=language or "unknown",
                content=code.strip(),
                description="目标用例生成可能依赖代码"
            ))

        return dependency_codes

    def _parse_similar_code_info(self, content: str) -> Optional[SimilarCodeInfo]:
        similar_match = re.search(self.section_patterns['similar_code_info'], content, re.DOTALL)
        if not similar_match:
            return None

        similar_content = similar_match.group(1)

        # 解析相似测试用例（特殊处理文件中的格式）
        similar_test_case = None
        # 查找从第二个相似文本测试用例描述到相似用例FT代码之间的内容
        test_case_pattern = r'## 相似文本测试用例描述\n### 测试标题(.*?)(?=## 相似用例FT代码|$)'
        test_case_match = re.search(test_case_pattern, similar_content, re.DOTALL)
        if test_case_match:
            section = test_case_match.group(1)
            # 提取测试标题（第一行）
            lines = section.strip().split('\n')
            if lines:
                test_title = lines[0].strip()

                # 重新构建完整的section内容用于其他字段解析
                full_section = f"### 测试标题\n{test_title}\n{section}"

                preconditions = self._extract_field_multiline(full_section, r'### 预[制置]条件[：:]?\s*\n(.+)')
                tc_steps = self._extract_field_multiline(full_section, r'### TC步骤[：:]?\s*\n(.*?)(?=### |$)')
                tc_expected_results = self._extract_field_multiline(full_section, r'### TC预期结果[：:]?\s*\n(.*?)(?=### |$)')
                expected_results = self._extract_field_multiline(full_section, r'### 预期结果[：:]?\s*\n(.*?)(?=### |$)')
                pass_criteria = self._extract_field_multiline(full_section, r'### (?:验收|通过)准则[：:]?\s*\n(.*?)(?=### |$)')

                similar_test_case = SimilarTestCase(
                    test_title=test_title,
                    preconditions=preconditions,
                    tc_steps=tc_steps,
                    tc_expected_results=tc_expected_results,
                    expected_results=expected_results,
                    pass_criteria=pass_criteria
                )

        # 解析相似FT代码
        similar_ft_code = []
        ft_code_sections = re.findall(r'## 相似用例FT代码(.*?)(?=## |$)', similar_content, re.DOTALL)
        for section in ft_code_sections:
            file_path_match = re.search(r'代码路径[：:]?\s*(.+)', section)
            file_path = file_path_match.group(1).strip() if file_path_match else ""

            code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', section, re.DOTALL)
            for language, code in code_blocks:
                similar_ft_code.append(CodeSnippet(
                    file_path=file_path,
                    language=language or "unknown",
                    content=code.strip(),
                    description="相似用例FT代码"
                ))

        # 解析相似用例代码依赖
        similar_dependency_code = []
        dep_code_sections = re.findall(r'## 相似用例代码依赖(.*?)(?=## |$)', similar_content, re.DOTALL)
        for section in dep_code_sections:
            file_path_match = re.search(r'代码路径[：:]?\s*(.+)', section)
            file_path = file_path_match.group(1).strip() if file_path_match else ""

            code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', section, re.DOTALL)
            for language, code in code_blocks:
                similar_dependency_code.append(CodeSnippet(
                    file_path=file_path,
                    language=language or "unknown",
                    content=code.strip(),
                    description="相似用例代码依赖"
                ))

        return SimilarCodeInfo(
            similar_test_case=similar_test_case,
            similar_ft_code=similar_ft_code,
            similar_dependency_code=similar_dependency_code
        )

    def _parse_generation_requirements(self, content: str) -> Optional[GenerationRequirements]:
        req_match = re.search(self.section_patterns['generation_requirements'], content, re.DOTALL)
        if not req_match:
            return None
        return GenerationRequirements(content=req_match.group(1).strip())

    def _parse_implementation_steps(self, content: str) -> Optional[ImplementationSteps]:
        steps_match = re.search(self.section_patterns['implementation_steps'], content, re.DOTALL)
        if not steps_match:
            return None
        return ImplementationSteps(content=steps_match.group(1).strip())

    def _parse_output_format(self, content: str) -> Optional[OutputFormat]:
        format_match = re.search(self.section_patterns['output_format'], content, re.DOTALL)
        if not format_match:
            return None
        return OutputFormat(content=format_match.group(1).strip())

    def _parse_output_requirements(self, content: str) -> Optional[OutputRequirements]:
        req_match = re.search(self.section_patterns['output_requirements'], content, re.DOTALL)
        if not req_match:
            return None
        return OutputRequirements(content=req_match.group(1).strip())

    def _parse_target_test_case_description(self, content: str) -> Optional[TargetTestCaseDescription]:
        desc_match = re.search(self.section_patterns['target_test_case_description'], content, re.DOTALL)
        if not desc_match:
            return None
        return TargetTestCaseDescription(content=desc_match.group(1).strip())
    
    def _extract_list_field(self, content: str, pattern: str) -> List[str]:
        match = re.search(pattern, content, re.DOTALL)
        if not match:
            return []
        
        text = match.group(1).strip()
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        return [re.sub(r'^[-*]\s*', '', line) for line in lines if line]